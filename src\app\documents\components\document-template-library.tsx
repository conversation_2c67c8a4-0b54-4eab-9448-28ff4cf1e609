'use client';

import {
  FileText,
  Search,
  Eye,
  Download,
  Edit,
  Trash2,
  Folder,
  X,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { DocumentTemplate } from '@/features/document/types';
import { getTemplateFolders } from '@/features/document/actions';
import { EditTemplateDialog } from './edit-template-dialog';
import { DeleteTemplateDialog } from './delete-template-dialog';
import { DocumentPreviewDialog } from './document-preview-dialog';

interface DocumentTemplateLibraryProps {
  templates: DocumentTemplate[];
  onTemplateUpdated: (template: DocumentTemplate) => void;
  onTemplateDeleted: (templateId: string) => void;
  onFilterChange?: (folderId: string | null) => void;
}

export function DocumentTemplateLibrary({
  templates,
  onTemplateUpdated,
  onTemplateDeleted,
  onFilterChange,
}: Readonly<DocumentTemplateLibraryProps>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [folders, setFolders] = useState<
    Array<{ id: string; name: string; count: number }>
  >([]);
  const [editingTemplate, setEditingTemplate] =
    useState<DocumentTemplate | null>(null);
  const [deletingTemplate, setDeletingTemplate] =
    useState<DocumentTemplate | null>(null);
  const [previewingTemplate, setPreviewingTemplate] =
    useState<DocumentTemplate | null>(null);

  const { execute: loadFolders } = useServerAction(getTemplateFolders, {
    onSuccess: ({ data }) => {
      setFolders(data);
    },
  });

  useEffect(() => {
    loadFolders();
  }, [loadFolders]);

  const handleEditTemplate = (template: DocumentTemplate) => {
    onTemplateUpdated(template);
    setEditingTemplate(null);
  };

  const handleDeleteTemplate = (templateId: string) => {
    onTemplateDeleted(templateId);
    setDeletingTemplate(null);
  };

  const handleFolderFilter = (folderId: string | null) => {
    setSelectedFolder(folderId);
    onFilterChange?.(folderId);
  };

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch = template.fileName
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesFolder =
      selectedFolder === null || template.folderId === selectedFolder;
    return matchesSearch && matchesFolder;
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Biblioteca de Plantillas</CardTitle>
              <CardDescription>
                Plantillas de documentos legales disponibles en el sistema
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar plantillas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-80 pl-10"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Folder Filter Buttons */}
          <div className="mb-6 flex flex-wrap gap-2">
            <Button
              variant={selectedFolder === null ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleFolderFilter(null)}
              className="flex items-center gap-2"
            >
              <Folder className="h-4 w-4" />
              Todas las carpetas
              <Badge variant="secondary" className="ml-1">
                {templates.length}
              </Badge>
            </Button>
            {folders.map((folder) => (
              <Button
                key={folder.id}
                variant={selectedFolder === folder.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleFolderFilter(folder.id)}
                className="flex items-center gap-2"
              >
                <Folder className="h-4 w-4" />
                {folder.name}
                <Badge variant="secondary" className="ml-1">
                  {folder.count}
                </Badge>
              </Button>
            ))}
            {selectedFolder && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFolderFilter(null)}
                className="flex items-center gap-1 text-gray-500"
              >
                <X className="h-4 w-4" />
                Limpiar filtro
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTemplates.map((template) => (
              <Card
                key={template.id}
                className="transition-shadow hover:shadow-md"
              >
                <CardContent className="p-6">
                  <div className="mb-3 flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <Badge variant="outline">
                        {template.mimeType.includes('word')
                          ? 'Word'
                          : 'Documento'}
                      </Badge>
                    </div>
                    <Badge variant="outline">
                      {template.placeholders.length} campos
                    </Badge>
                  </div>

                  <h3 className="mb-2 font-semibold">{template.fileName}</h3>
                  <p className="mb-4 text-sm text-gray-600">
                    Plantilla de documento con {template.placeholders.length}{' '}
                    campos
                  </p>

                  <div className="mb-4 flex items-center justify-between text-xs text-gray-500">
                    <span>
                      Actualizado:{' '}
                      {template.updatedAt.toLocaleDateString('es-CO')}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => setPreviewingTemplate(template)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Vista Previa
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/download`;
                        window.open(url, '_blank');
                      }}
                      title="Descargar"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingTemplate(template)}
                      title="Editar"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDeletingTemplate(template)}
                      title="Eliminar"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Diálogos */}

      {editingTemplate && (
        <EditTemplateDialog
          template={editingTemplate}
          open={!!editingTemplate}
          onOpenChange={(open) => !open && setEditingTemplate(null)}
          onTemplateUpdated={handleEditTemplate}
        />
      )}

      <DeleteTemplateDialog
        template={deletingTemplate}
        open={!!deletingTemplate}
        onOpenChange={(open) => !open && setDeletingTemplate(null)}
        onTemplateDeleted={handleDeleteTemplate}
      />

      <DocumentPreviewDialog
        template={previewingTemplate}
        open={!!previewingTemplate}
        onOpenChange={(open) => !open && setPreviewingTemplate(null)}
      />
    </div>
  );
}
